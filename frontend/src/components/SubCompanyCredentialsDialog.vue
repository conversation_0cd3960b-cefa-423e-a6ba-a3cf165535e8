<script setup lang="ts">
import { Copy } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'vue-sonner'

const props = defineProps<{
  visitCreationApiKey: string
  visitWebhookApiKey: string
}>()

const emit = defineEmits<{
  (e: 'update:open', value: boolean): void
}>()

function copyVisitCreationApiKey() {
  navigator.clipboard.writeText(props.visitCreationApiKey)
  toast.success('Visit Creation API Key copied to clipboard')
}

function copyVisitWebhookApiKey() {
  navigator.clipboard.writeText(props.visitWebhookApiKey)
  toast.success('Visit Webhook API Key copied to clipboard')
}
</script>

<template>
  <Dialog @update:open="emit('update:open', $event)">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle>Visit Authorization Keys</DialogTitle>
        <DialogDescription>
          Please add these authorization keys to sub company environment variables or copy and save
          it for future use.
        </DialogDescription>
      </DialogHeader>
      <div class="grid gap-4">
        <div v-if="visitCreationApiKey" class="flex items-end space-x-2 mt-3">
          <div class="grid flex-1 gap-2">
            <Label for="visit_creation_api_key"> Visit Creation API Key </Label>
            <Input id="visit_creation_api_key" :model-value="visitCreationApiKey" readonly />
          </div>
          <Button type="submit" size="icon" class="px-3" @click="copyVisitCreationApiKey">
            <span class="sr-only">Copy</span>
            <Copy class="w-4 h-4" />
          </Button>
        </div>
        <div v-if="visitWebhookApiKey" class="flex items-end space-x-2">
          <div class="grid flex-1 gap-2">
            <Label for="visit_webhook_api_key"> Visit Webhook API Key </Label>
            <Input id="visit_webhook_api_key" :model-value="visitWebhookApiKey" readonly />
          </div>
          <Button type="submit" size="icon" class="px-3" @click="copyVisitWebhookApiKey">
            <span class="sr-only">Copy</span>
            <Copy class="w-4 h-4" />
          </Button>
        </div>
      </div>
      <DialogFooter class="sm:justify-start mt-4">
        <DialogClose as-child>
          <Button type="button" variant="secondary"> Close </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
