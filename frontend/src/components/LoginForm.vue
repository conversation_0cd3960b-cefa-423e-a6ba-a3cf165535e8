<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Form, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { apiClient } from '@/composables/useApi'
import { useAuthStore } from '@/stores/authStore'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import Alert from './ui/alert/Alert.vue'
import AlertDescription from './ui/alert/AlertDescription.vue'
import { processErrors } from '@/lib/utils'

const formSchema = toTypedSchema(
  z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(1, 'Password is required'),
  }),
)

interface LoginResponse {
  status: number
  accessToken?: string
  '2fa_enabled'?: 0 | 1
  userData?: {
    full_name: string
    email: string
  }
  message?: string
  errors?: Record<string, string[]>
}

interface LoginPayload {
  email: string
  password: string
  authentication_code?: string
}

const isLoading = ref(false)
const loginError = ref('')
const router = useRouter()
const authStore = useAuthStore()
const { setFieldError } = useForm()
const is2faEnabled = ref(false)
const authenticationCode = ref('')

async function onSubmit(values: unknown) {
  const formValues = values as LoginPayload
  isLoading.value = true
  loginError.value = ''

  if (is2faEnabled.value) {
    formValues.authentication_code = authenticationCode.value || ''
  }

  try {
    const { data } = await apiClient.post<LoginResponse>('/admin/login', formValues)

    if (data.status === 200) {
      if (data['2fa_enabled'] && data['2fa_enabled'] === 1) {
        is2faEnabled.value = true
        isLoading.value = false

        return
      }

      if (data.accessToken && data.userData) {
        authStore.setToken(data.accessToken)

        const user = {
          id: 'admin',
          email: data.userData.email || formValues.email,
          name: data.userData.full_name,
        }

        authStore.setUser(user)
        router.push({ name: 'dashboard' })
      } else {
        loginError.value = data.message || 'An error occurred during login. Please try again.'
      }
    } else if (data.errors) {
      Object.entries(data.errors).forEach(([key, value]) => {
        setFieldError(key, value[0])
      })
    } else {
      loginError.value = data.message || 'An error occurred during login. Please try again.'
    }
  } catch (err) {
    console.error('Login error:', err)
    loginError.value = processErrors(err)
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <Card class="w-full max-w-sm">
    <CardHeader>
      <CardTitle class="text-2xl"> Admin Login </CardTitle>
      <CardDescription> Enter your credentials to login into your account. </CardDescription>
    </CardHeader>

    <Form :validation-schema="formSchema" @submit="onSubmit">
      <CardContent class="grid gap-4">
        <!-- Authentication Code -->
        <FormField name="authentication_code" v-slot="{ errorMessage }">
          <FormItem :class="is2faEnabled ? '' : 'hidden'">
            <FormLabel v-if="is2faEnabled">2FA Code</FormLabel>
            <FormControl>
              <Input
                v-model="authenticationCode"
                :type="is2faEnabled ? 'text' : 'hidden'"
                placeholder="000000"
                :aria-invalid="!!errorMessage"
                autocomplete="off"
                autofocus
                maxlength="6"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <!-- Email Field -->
        <FormField name="email" v-slot="{ componentField, errorMessage }">
          <FormItem :class="!is2faEnabled ? '' : 'hidden'">
            <FormLabel v-if="!is2faEnabled">Email</FormLabel>
            <FormControl>
              <Input
                v-bind="componentField"
                :type="is2faEnabled ? 'hidden' : 'email'"
                placeholder="<EMAIL>"
                :aria-invalid="!!errorMessage"
                autocomplete="email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <!-- Password Field -->
        <FormField name="password" v-slot="{ componentField, errorMessage }">
          <FormItem :class="!is2faEnabled ? '' : 'hidden'">
            <FormLabel v-if="!is2faEnabled">Password</FormLabel>
            <FormControl>
              <Input
                v-bind="componentField"
                :type="is2faEnabled ? 'hidden' : 'password'"
                :aria-invalid="!!errorMessage"
                autocomplete="current-password"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>

        <!-- Error Message -->
        <Alert v-if="loginError" variant="destructive">
          <AlertDescription>{{ loginError }}</AlertDescription>
        </Alert>
      </CardContent>

      <CardFooter class="mt-6">
        <Button class="w-full" type="submit" :disabled="isLoading">
          <span v-if="isLoading">Signing in...</span>
          <span v-else>Sign in</span>
        </Button>
      </CardFooter>
    </Form>
  </Card>
</template>
