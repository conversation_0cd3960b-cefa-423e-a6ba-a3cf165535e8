<script setup lang="ts">
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  // AlertDialogTrigger,
} from '@/components/ui/alert-dialog'

const props = defineProps<{
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  onConfirm?: () => void
}>()

const emit = defineEmits<{
  (e: 'confirm'): void
}>()

function handleConfirm() {
  if (typeof props.onConfirm === 'function') {
    props.onConfirm()
  }
  emit('confirm')
}
</script>

<template>
  <AlertDialog>
    <!-- <AlertDialogTrigger>
      <slot name="trigger">
        <button>Open</button>
      </slot>
    </AlertDialogTrigger> -->

    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>{{ title || 'Are you sure?' }}</AlertDialogTitle>
        <AlertDialogDescription>
          {{ description || 'This action cannot be undone.' }}
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel>{{ cancelText || 'Cancel' }}</AlertDialogCancel>
        <AlertDialogAction @click="handleConfirm">{{
          confirmText || 'Continue'
        }}</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>
