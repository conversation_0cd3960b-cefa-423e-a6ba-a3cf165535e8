<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { ChevronRight } from 'lucide-vue-next'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar'
import type { MenuConfig, MenuItem, SubMenuItem } from '@/config/menuItems'

defineProps<{
  menuConfig: MenuConfig
}>()

const route = useRoute()
const router = useRouter()

const isMenuItemActive = (item: MenuItem): boolean => {
  // Direct match
  if (item.routeName && route.name === item.routeName) {
    return true
  }

  // Parent route match via meta.parentRouteName
  if (item.routeName && route.meta?.parentRouteName === item.routeName) {
    return true
  }

  // Check submenus
  if (item.items && item.items.length > 0) {
    return item.items.some((subItem) => isSubMenuItemActive(subItem))
  }

  return false
}

const isSubMenuItemActive = (subItem: SubMenuItem): boolean => {
  if (subItem.routeName === route.name) {
    return true
  }

  if (route.meta?.parentRouteName === subItem.routeName) {
    return true
  }

  return false
}
</script>

<template>
  <!-- Iterate through each menu group -->
  <template v-for="group in menuConfig" :key="group.label || 'group'">
    <SidebarGroup>
      <!-- Only render the label if it exists -->
      <SidebarGroupLabel v-if="group.label">{{ group.label }}</SidebarGroupLabel>
      <SidebarMenu>
        <Collapsible
          v-for="item in group.items"
          :key="item.title"
          as-child
          :default-open="item.isActive || isMenuItemActive(item)"
          class="group/collapsible"
        >
          <SidebarMenuItem :active="isMenuItemActive(item)">
            <CollapsibleTrigger as-child>
              <SidebarMenuButton
                :tooltip="item.title"
                @click="item.routeName && router.push({ name: item.routeName })"
                :is-active="isMenuItemActive(item)"
                class="cursor-pointer"
              >
                <component :is="item.icon" v-if="item.icon" />
                <span>{{ item.title }}</span>
                <ChevronRight
                  v-if="item.items && item.items.length > 0"
                  class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                />
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent v-if="item.items && item.items.length > 0">
              <SidebarMenuSub>
                <SidebarMenuSubItem
                  v-for="subItem in item.items"
                  :key="subItem.title"
                  :active="isSubMenuItemActive(subItem)"
                >
                  <SidebarMenuSubButton as-child :is-active="isSubMenuItemActive(subItem)">
                    <RouterLink :to="{ name: subItem.routeName }">
                      <span>{{ subItem.title }}</span>
                    </RouterLink>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      </SidebarMenu>
    </SidebarGroup>
  </template>
</template>
