import { ref, onMounted } from 'vue'
import { apiClient } from './useApi'
import type { ApiResponse } from '@/types/api'

export interface State {
  name: string
  code: string
}

export function useStateList() {
  const states = ref<State[]>([])
  const isLoadingStates = ref(false)
  const error = ref<string | null>(null)

  const fetchStates = async () => {
    if (states.value.length > 0) {
      return states.value
    }

    isLoadingStates.value = true
    error.value = null

    try {
      const { data } = await apiClient.get<ApiResponse & { states: State[] }>('/admin/state-list')

      if (data && data.status === 200 && Array.isArray(data.states)) {
        states.value = data.states
      } else {
        error.value = 'Failed to load states'
      }
    } catch (err) {
      console.error('Error fetching states:', err)
      error.value = 'An error occurred while fetching states'
    } finally {
      isLoadingStates.value = false
    }

    return states.value
  }

  onMounted(() => {
    fetchStates()
  })

  return {
    states,
    isLoadingStates,
    error,
    fetchStates,
  }
}
