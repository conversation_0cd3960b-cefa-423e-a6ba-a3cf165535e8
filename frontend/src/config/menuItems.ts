import type { LucideIcon } from 'lucide-vue-next'
import {
  Blocks,
  Building2,
  ClipboardPlus,
  HospitalIcon,
  LayoutDashboard,
  ShieldPlus,
  MapPin,
} from 'lucide-vue-next'

export interface SubMenuItem {
  title: string
  routeName: string
}

export interface MenuItem {
  title: string
  routeName?: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SubMenuItem[]
}

export interface MenuGroup {
  label?: string
  items: MenuItem[]
}

export type MenuConfig = MenuGroup[]

export const menuItems: MenuConfig = [
  {
    label: 'Platform',
    items: [
      {
        title: 'Dashboard',
        routeName: 'dashboard',
        icon: LayoutDashboard,
        isActive: true,
      },
      {
        title: 'Sub Companies',
        icon: Building2,
        routeName: 'sub-companies',
      },
      {
        title: 'Treatments',
        icon: ShieldPlus,
        routeName: 'treatments',
      },
      // {
      //   title: 'Pharmacies',
      //   icon: HospitalIcon,
      //   routeName: 'pharmacies',
      // },
      {
        title: 'Beluga Visits',
        icon: ClipboardPlus,
        routeName: 'beluga-visits',
      },
      {
        title: 'GoGoMeds Orders',
        icon: Blocks,
        routeName: 'gogomeds-orders',
      },
      {
        title: 'States',
        icon: MapPin,
        routeName: 'states',
      },
    ],
  },
  // {
  //   label: 'Application',
  //   items: [
  //     {
  //       title: 'Settings',
  //       url: '#',
  //       icon: Settings2,
  //       items: [
  //         {
  //           title: 'General',
  //           url: '#',
  //         },
  //       ],
  //     },
  //   ],
  // },
]
