import { useAuthStore } from '@/stores/authStore'
import type { Middleware } from './types'

export const auth: Middleware = async ({ to, next }) => {
  const authStore = useAuthStore()

  if (!authStore.isAuthenticated) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath },
    })
  }

  // Verify token if authenticated
  const isValid = await authStore.verifyToken()

  if (!isValid) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath },
    })
  }

  return next()
}

export const guest: Middleware = ({ next }) => {
  const authStore = useAuthStore()

  if (authStore.isAuthenticated) {
    return next({ name: 'dashboard' })
  }

  return next()
}
