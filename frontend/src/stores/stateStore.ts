import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'

// types
export type State = {
  id: string
  name: string
  code: string
  treatments: {
    name: 'ED' | 'HL' | 'WL'
    visit_type: 'asynchronous' | 'synchronous'
  }[]
  type: 'state' | 'territory'
  is_active: 0 | 1
  created_date: string
  created_time: string

  // custom field
  status: boolean
}

export type StateDetail = Omit<State, 'created_date' | 'created_time'>

export type StateListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type StateListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: State[]
}

export type StatePayload = {
  name: string
  code: string
  treatments: {
    name: 'ED' | 'HL' | 'WL'
    visit_type: 'asynchronous' | 'synchronous'
  }[]
  type: 'state' | 'territory'
}

// store definition
export const useStateStore = defineStore('stateStore', () => {
  // state
  const states = ref<State[]>([])
  const selectedState = ref<StateDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const isTogglingStatus = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  // getters
  const listPayload = computed<Partial<StateListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  // actions
  async function fetchStates() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { stateData: StateListResponse }>(
        '/admin/state-list',
        listPayload.value,
      )

      if (response.data.status === 200) {
        states.value = response.data.stateData.records.map((state) => ({
          ...state,
          status: Boolean(state.is_active),
        }))

        pagination.value = {
          currentPage: response.data.stateData.current_page,
          perPage: response.data.stateData.per_page,
          totalPages: response.data.stateData.totalPage,
          totalRecords: response.data.stateData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch states'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching states:', err)
      error.value = processErrors(err, 'An error occurred while fetching states.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getStateById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { stateData: StateDetail }>(
        `/admin/view-state`,
        { id },
      )

      if (response.data.status === 200) {
        selectedState.value = {
          ...response.data.stateData,
          status: Boolean(response.data.stateData.is_active),
        }
        return true
      } else {
        error.value = response.data.message || `Failed to fetch state with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching state details:', err)
      error.value = processErrors(err, 'An error occurred while fetching state details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addState(payload: StatePayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/add-state', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'State added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add state'
        return false
      }
    } catch (err: any) {
      console.error('Error adding state:', err)
      error.value = processErrors(err, 'An error occurred while adding state.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updateState(payload: StatePayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-state', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'State updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update state'
        return false
      }
    } catch (err: any) {
      console.error('Error updating state:', err)
      error.value = processErrors(err, 'An error occurred while updating state.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function toggleStateStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/update-state-status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'State status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update state status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating state status:', err)
      error.value = processErrors(err, 'An error occurred while updating state status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function deleteState(id: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.delete<ApiResponse>(`/admin/delete-state/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'State deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete state'
        return false
      }
    } catch (err: any) {
      console.error('Error deleting state:', err)
      error.value = processErrors(err, 'An error occurred while deleting state.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  return {
    // State
    states,
    selectedState,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isTogglingStatus,
    isDeleting,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    dateRange,
    sortBy,

    // Actions
    fetchStates,
    getStateById,
    addState,
    updateState,
    toggleStateStatus,
    deleteState,
  }
})
