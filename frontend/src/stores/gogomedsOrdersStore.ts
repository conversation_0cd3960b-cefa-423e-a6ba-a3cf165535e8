import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'
import { type SubCompany } from './subCompanyStore'

export enum GogomedsOrderStatus {
  PENDING = 0,
  DELIVERED = 1,
  CANCELED = 2,
  NEW = 4,
  PROCESSING = 5,
  WAITING_FOR_SHIPMENT = 6,
  PREPARING_FOR_SHIPMENT = 7,
  IN_TRANSIT = 8,
  OUT_FOR_DELIVERY = 9,
}

// types
export type GogomedsOrder = {
  id: string
  sub_company_order_no: string
  pharmacy_order_no: string
  status: GogomedsOrderStatus
  order_type: string
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
  order_status_text: string
  sub_company: Pick<SubCompany, 'company_name'>
}

export type GogomedsOrderListPayload = {
  searchQuery?: string
  perPage?: number
  page?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
  from_date?: string
  to_date?: string
  sub_company_id?: string
}

export type GogomedsOrderListResponse = {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: GogomedsOrder[]
}

export type GogomedsOrderDetailResponse = {
  is_show_pharmacy_detail_information: number
  order_details: {
    sub_company_order_no: string
    pharmacy_order_no: string
    status: GogomedsOrderStatus
    order_status_text: string
    order_type: string
    created_at: string
    updated_at: string
  }
  drugs: {
    ndc: string
    full_medicine_name: string
    medicine_name: string
    refill_script_number: string
    dose: string
    form: string
    packaging: string
    qty: string
    prescriber: {
      first_name: string
      last_name: string
      address?: {
        Line1: string
        Line2?: string
        City: string
        State: string
        Zip: string
      }
      fax_number: string | null
      npi: string
    }
    order_item_status: {
      status_name: string
      status_set_by: string
      status_set_date: string
    }[]
    prescription: {
      written_date: string
      written_qty: string
      script_no: string
      dispense_history: {
        dispense_qty: string
        dispense_date: string
      }[]
    }
  }[]
  shipment_details: {
    tracking_url: string | null
    shipment_method: string
    tracking_number: string | null
    delivery_provider: string | null
  }
  order_status_history: {
    status: string
    status_set_by: string
    status_set_date: string
  }[]
  order_ship_details: {
    EventDescription: string
    EventStatus: string
    EventStatusText: string
    EventLocation: string
    EventDateTime: string
    EventDate: string
  }[]
  pharmacy_logs: {
    id: string
    request_from: string
    request_to: string
    event_status_code: string | null
    created_date: string
    created_time: string
    updated_date: string
    updated_time: string
  }[]
  sub_company_pharmacy_logs: {
    id: string
    event: string
    request_from: string
    request_to: string
    response_code: number | null
    created_date: string
    created_time: string
    updated_date: string
    updated_time: string
  }[]
}

// store definition
export const useGogomedsOrdersStore = defineStore('gogomedsOrdersStore', () => {
  // state
  const isLoadingList = ref(false)
  const gogomedsOrders = ref<GogomedsOrder[]>([])
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const filterSubCompanyId = ref<string>('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })
  const error = ref<string | null>(null)

  const gogomedsOrderDetail = ref<GogomedsOrderDetailResponse>()
  const isLoadingOrderDetail = ref(false)

  const orderEventLogDetail = ref<object>({})
  const isLoadingOrderEventLogDetail = ref(false)

  // getters
  const listPayload = computed<Partial<GogomedsOrderListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    from_date: dateRange.value.start
      ? `${dateRange.value.start.month}/${dateRange.value.start.day}/${dateRange.value.start.year}`
      : undefined,
    to_date: dateRange.value.end
      ? `${dateRange.value.end.month}/${dateRange.value.end.day}/${dateRange.value.end.year}`
      : undefined,
    sub_company_id: filterSubCompanyId.value !== 'all' ? filterSubCompanyId.value : undefined,
  }))

  // actions
  async function fetchGogomedsOrders() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { visitList: GogomedsOrderListResponse }>(
        '/admin/gogomeds-order-list',
        listPayload.value,
      )

      if (response.data.status === 200) {
        gogomedsOrders.value = response.data.visitList.records

        pagination.value = {
          currentPage: response.data.visitList.current_page,
          perPage: response.data.visitList.per_page,
          totalPages: response.data.visitList.totalPage,
          totalRecords: response.data.visitList.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch gogomeds orders'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching gogomeds orders:', err)
      error.value = processErrors(err, 'An error occurred while fetching gogomeds orders.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function fetchGogomedsOrderDetail(id: string) {
    isLoadingOrderDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & { orderDetails: GogomedsOrderDetailResponse }
      >(`/admin/gogomeds-order-details/${id}`)

      if (response.data.status === 200) {
        gogomedsOrderDetail.value = response.data.orderDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch gogomeds order detail`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching gogomeds order detail:', err)
      error.value = processErrors(err, 'An error occurred while fetching gogomeds order detail.')
      return false
    } finally {
      isLoadingOrderDetail.value = false
    }
  }

  async function retrieveOrderDetailsFromGogomeds(id: string) {
    isLoadingOrderDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/fetch-gogomeds-details/${id}`)

      if (response.data.status === 200) {
        await fetchGogomedsOrderDetail(id)
        return true
      } else {
        error.value = response.data.message || `Failed to retrieve order details from gogomeds`
        return false
      }
    } catch (err: any) {
      console.error('Error retrieving order details from gogomeds:', err)
      error.value = processErrors(
        err,
        'An error occurred while retrieving order details from gogomeds.',
      )
      return false
    } finally {
      isLoadingOrderDetail.value = false
    }
  }

  async function fetchGogomedsOrderEventLogDetail(id: string, type: 'gogomeds' | 'sub_company') {
    isLoadingOrderEventLogDetail.value = true
    error.value = null

    try {
      const url =
        type === 'gogomeds' ? `/admin/gogomeds-log/${id}` : `/admin/sub-company-pharmacy-log/${id}`
      const response = await apiClient.get<ApiResponse & { log: unknown }>(url)

      if (response.data.status === 200) {
        orderEventLogDetail.value = response.data.log as object
        return true
      } else {
        error.value = response.data.message || `Failed to fetch visit log detail`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching visit log detail:', err)
      error.value = processErrors(err, 'An error occurred while fetching visit log detail.')
      return false
    } finally {
      isLoadingOrderEventLogDetail.value = false
    }
  }

  function resolveGogomedsOrderStatusColor(status: GogomedsOrderStatus) {
    switch (status) {
      case GogomedsOrderStatus.PENDING:
        return 'bg-yellow-500/50'
      case GogomedsOrderStatus.DELIVERED:
        return 'bg-green-500/50'
      case GogomedsOrderStatus.CANCELED:
        return 'bg-red-500/50'
      case GogomedsOrderStatus.NEW:
        return 'bg-blue-500/50'
      case GogomedsOrderStatus.PROCESSING:
        return 'bg-blue-500/50'
      case GogomedsOrderStatus.WAITING_FOR_SHIPMENT:
        return 'bg-blue-500/50'
      case GogomedsOrderStatus.PREPARING_FOR_SHIPMENT:
        return 'bg-blue-500/50'
      case GogomedsOrderStatus.IN_TRANSIT:
        return 'bg-blue-500/50'
      case GogomedsOrderStatus.OUT_FOR_DELIVERY:
        return 'bg-blue-500/50'
      default:
        return 'bg-gray-500/50'
    }
  }

  return {
    // State
    gogomedsOrders,
    pagination,
    isLoadingList,
    searchQuery,
    filterSubCompanyId,
    dateRange,
    sortBy,
    error,
    gogomedsOrderDetail,
    isLoadingOrderDetail,
    orderEventLogDetail,
    isLoadingOrderEventLogDetail,

    // Actions
    fetchGogomedsOrders,
    fetchGogomedsOrderDetail,
    fetchGogomedsOrderEventLogDetail,
    resolveGogomedsOrderStatusColor,
    retrieveOrderDetailsFromGogomeds,
  }
})
