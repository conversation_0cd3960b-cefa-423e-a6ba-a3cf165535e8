import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import { apiClient } from '@/composables/useApi'
import { toast } from 'vue-sonner'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type { DateRange } from 'reka-ui'
import type { PharmacyOption } from './pharmacyStore'

export interface SubCompany {
  id: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  company_name: string
  company_id: string
  beluga_sub_company: string
  is_active: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
  // custom fields
  status: boolean
}

export interface SubCompanyDetail extends SubCompany {
  company_website_url: string
  app_base_url: string
  visit_prefix: string
  address_line_1: string
  address_line_2: string | null
  city: string
  state: string
  country: string
  zipcode: string
  treatment_ids: string[]
  created_date_time: string
  updated_date_time: string
  action_by_user_name: string
  action_at: string
  sub_company_pharmacies: {
    id: string
    pharmacy_id: string
    pharmacy: PharmacyOption
  }[]
}

export interface SubCompanyListResponse {
  current_page: number
  per_page: number
  totalPage: number
  totalRecords: number
  records: SubCompany[]
}

export interface SubCompanyListPayload {
  searchQuery?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: string
  from_date?: string
  to_date?: string
  is_active?: string | number
}

export type SubCompanyPayload = {
  first_name: string
  last_name: string
  email: string
  phone_number: string
  company_name: string
  company_id: string
  company_website_url: string
  app_base_url: string
  beluga_sub_company: string
  visit_prefix: string
  pharmacies: {
    id: string
    username?: string
    password?: string
  }[]
  treatment_ids: string[]
  address_line_1: string
  address_line_2?: string
  city: string
  state: string
  country: string
  zipcode: string
}

export const useSubCompanyStore = defineStore('subCompany', () => {
  // State
  const subCompanies = ref<SubCompany[]>([])
  const isLoadingList = ref(false)
  const searchQuery = ref('')
  const statusFilter = ref('all')
  const dateRange = ref({
    start: undefined,
    end: undefined,
  }) as Ref<DateRange>
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const selectedSubCompany = ref<SubCompanyDetail | null>(null)
  const isLoadingDetails = ref(false)
  const isTogglingStatus = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isDeleting = ref(false)
  const visitCreationApiKey = ref<string>('')
  const visitWebhookApiKey = ref<string>('')
  const isRegeneratingApiKey = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})

  // getters
  const listPayload = computed<Partial<SubCompanyListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    from_date: dateRange.value.start
      ? `${dateRange.value.start.month}/${dateRange.value.start.day}/${dateRange.value.start.year}`
      : undefined,
    to_date: dateRange.value.end
      ? `${dateRange.value.end.month}/${dateRange.value.end.day}/${dateRange.value.end.year}`
      : undefined,
    is_active: statusFilter.value !== 'all' ? statusFilter.value : undefined,
  }))

  // Actions
  const fetchSubCompanies = async () => {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse & { stateData: SubCompanyListResponse }>(
        '/admin/list-sub-company',
        listPayload.value,
      )

      if (response.data.status === 200) {
        subCompanies.value = response.data.stateData.records.map((subCompany) => ({
          ...subCompany,
          status: Boolean(subCompany.is_active),
        }))

        pagination.value = {
          currentPage: response.data.stateData.current_page,
          perPage: response.data.stateData.per_page,
          totalPages: response.data.stateData.totalPage,
          totalRecords: response.data.stateData.totalRecords,
        }
        return true
      } else {
        error.value = response.data.message || 'Failed to fetch treatments'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching treatments:', err)
      error.value = processErrors(err, 'An error occurred while fetching treatments.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getSubCompanyById(id: string) {
    isLoadingDetails.value = true
    try {
      const response = await apiClient.get<
        ApiResponse & {
          subCompanyDetails?: SubCompanyDetail
        }
      >(`/admin/sub-company/${id}`)
      if (response.data.status === 200 && response.data.subCompanyDetails) {
        selectedSubCompany.value = {
          ...response.data.subCompanyDetails,
          status: Boolean(response.data.subCompanyDetails.is_active),
        }
        return true
      } else {
        error.value = response.data.message || 'Failed to fetch sub-company details'
        return false
      }
    } catch (err: unknown) {
      console.error('Error fetching sub-company details:', err)
      error.value = processErrors(err, 'An error occurred while fetching sub-company details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  const toggleSubCompanyStatus = async (subCompanyId: string) => {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(
        `/admin/update-sub-company-status/${subCompanyId}`,
      )
      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-company status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update status'
        return false
      }
    } catch (err: unknown) {
      console.error('Error toggling sub-company status:', err)
      error.value = processErrors(err, 'An error occurred while updating the sub-company status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function addSubCompany(payload: SubCompanyPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<
        ApiResponse & {
          visit_creation_authorization_key: string
          visit_webhook_authorization_key: string
        }
      >('/admin/add-sub-company', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub Company added successfully')
        visitCreationApiKey.value = response.data.visit_creation_authorization_key || ''
        visitWebhookApiKey.value = response.data.visit_webhook_authorization_key || ''
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add sub company'
        return false
      }
    } catch (err: any) {
      console.error('Error adding sub company:', err)
      error.value = processErrors(err, 'An error occurred while adding sub company.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updateSubCompany(payload: SubCompanyPayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-sub-company', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub Company updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update sub company'
        return false
      }
    } catch (err: unknown) {
      console.error('Error updating sub company:', err)
      error.value = processErrors(err, 'An error occurred while updating sub company.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  const deleteSubCompany = async (subCompanyId: string) => {
    try {
      isDeleting.value = true
      error.value = null

      const response = await apiClient.delete<ApiResponse>(
        `/admin/delete-sub-company/${subCompanyId}`,
      )
      if (response.data.status === 200) {
        toast.success(response.data.message || 'Sub-company deleted successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to delete sub-company'
        return false
      }
    } catch (err: unknown) {
      console.error('Error deleting sub-company:', err)
      error.value = processErrors(err, 'An error occurred while deleting the sub-company.')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  async function regenerateApiKey(id: string, type: 'visit' | 'webhook') {
    isRegeneratingApiKey.value = true
    error.value = null

    let endpoint = ''
    if (type === 'visit') {
      endpoint = `/admin/re-generate-visit-authorize-key/${id}`
    } else if (type === 'webhook') {
      endpoint = `/admin/re-generate-visit-webhook-key/${id}`
    }

    try {
      const response = await apiClient.get<
        ApiResponse & {
          visit_creation_authorization_key?: string
          visit_webhook_authorization_key?: string
        }
      >(endpoint)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'API keys updated successfully')
        visitCreationApiKey.value = response.data.visit_creation_authorization_key || ''
        visitWebhookApiKey.value = response.data.visit_webhook_authorization_key || ''
        return true
      } else {
        error.value = response.data.message || 'Failed to update API key'
        return false
      }
    } catch (err: any) {
      console.error('Error updating API keys:', err)
      error.value = processErrors(err, 'An error occurred while updating API keys.')
      return false
    } finally {
      isRegeneratingApiKey.value = false
    }
  }

  return {
    // State
    isLoadingList,
    subCompanies,
    searchQuery,
    statusFilter,
    dateRange,
    sortBy,
    pagination,
    selectedSubCompany,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isDeleting,
    isTogglingStatus,
    isRegeneratingApiKey,
    visitCreationApiKey,
    visitWebhookApiKey,
    error,
    fieldErrors,

    // Actions
    fetchSubCompanies,
    getSubCompanyById,
    toggleSubCompanyStatus,
    addSubCompany,
    updateSubCompany,
    deleteSubCompany,
    regenerateApiKey,
  }
})
