import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import type { User } from '@/types/api'
import { apiClient } from '@/composables'
import router from '@/router'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(Cookies.get('auth_token') || null)
  const user = ref<User | null>(
    Cookies.get('user_data') ? JSON.parse(Cookies.get('user_data') as string) : null,
  )
  const isAuthenticated = computed(() => !!token.value)
  const isVerifying = ref(false)

  const cookieOptions: Cookies.CookieAttributes = {
    // Use secure cookies in production
    secure: import.meta.env.PROD,
    // Use SameSite=Lax for better security
    sameSite: 'Lax',
    // Set to 30 days by default
    expires: 30,
  }

  function setToken(newToken: string) {
    token.value = newToken
    Cookies.set('auth_token', newToken, cookieOptions)
  }

  function setUser(userData: User) {
    user.value = userData
    Cookies.set('user_data', JSON.stringify(userData), cookieOptions)
  }

  function clearAuth() {
    token.value = null
    user.value = null
    Cookies.remove('auth_token')
    Cookies.remove('user_data')
  }

  async function verifyToken(): Promise<boolean> {
    if (!token.value || isVerifying.value) return false

    try {
      isVerifying.value = true
      const { data } = await apiClient.get('/admin/verify-token')

      if (data.status === 200) {
        return true
      }

      clearAuth()
      return false
    } catch (error) {
      console.error('Token verification failed:', error)
      clearAuth()
      return false
    } finally {
      isVerifying.value = false
    }
  }

  async function logout() {
    try {
      await apiClient.get('/admin/logout')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      clearAuth()
      router.push({ name: 'login' })
    }
  }

  return {
    token,
    user,
    isAuthenticated,
    isVerifying,
    setToken,
    setUser,
    clearAuth,
    logout,
    verifyToken,
  }
})
