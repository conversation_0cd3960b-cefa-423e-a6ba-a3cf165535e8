/**
 * Format a date to a human-readable string.
 *
 * @param date - The date to format.
 * @param locale - The locale to use for formatting. Defaults to 'en-US'.
 * @returns A human-readable string representing the date.
 */
export function formatDate(date: Date | string, locale = 'en-US'): string {
  return new Date(date).toLocaleDateString(locale)
}

/**
 * Calculate a string representing the time elapsed since a given date.
 *
 * @param date - The date to calculate the time elapsed since.
 * @returns A string representing the time elapsed since the given date.
 */
export function timeAgo(date: Date | string): string {
  const diff = (new Date().getTime() - new Date(date).getTime()) / 1000
  if (diff < 60) return 'just now'
  if (diff < 3600) return `${Math.floor(diff / 60)} min ago`
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`
  return `${Math.floor(diff / 86400)} days ago`
}
