<script setup lang="ts">
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Lock, Shield } from 'lucide-vue-next'
import ChangePasswordForm from './tabs/ChangePasswordForm.vue'
import TwoFactorAuth from './tabs/TwoFactorAuth.vue'
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center gap-3">
      <h1 class="text-2xl font-bold">Account Settings</h1>
    </div>

    <Tabs default-value="password" class="space-y-4">
      <TabsList class="grid w-full grid-cols-2">
        <TabsTrigger value="password" class="gap-2">
          <Lock class="h-4 w-4" />
          Change Password
        </TabsTrigger>
        <TabsTrigger value="2fa" class="gap-2">
          <Shield class="h-4 w-4" />
          Two Factor Authentication
        </TabsTrigger>
      </TabsList>

      <TabsContent value="password" class="space-y-4">
        <ChangePasswordForm />
      </TabsContent>

      <TabsContent value="2fa" class="space-y-4">
        <TwoFactorAuth />
      </TabsContent>
    </Tabs>
  </div>
</template>
