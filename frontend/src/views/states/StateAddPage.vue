<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useStateStore, type StatePayload } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { storeToRefs } from 'pinia'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'

const stateStore = useStateStore()
const { isAdding, error: storeError, fieldErrors } = storeToRefs(stateStore)
const { addState } = stateStore

const router = useRouter()
const formError = ref('')
const treatmentOptions = ref([
  { name: 'ED', visit_type: 'asynchronous', status: false },
  { name: 'HL', visit_type: 'asynchronous', status: false },
  { name: 'WL', visit_type: 'asynchronous', status: false },
])

const typeOptions = [
  { label: 'State', value: 'state' },
  { label: 'Territory', value: 'territory' },
]
const visitTypeOptions = [
  { label: 'Asynchronous', value: 'asynchronous' },
  { label: 'Synchronous', value: 'synchronous' },
]

const formSchema = toTypedSchema(
  z.object({
    name: z.string().min(1, 'Name is required'),
    code: z.string().min(1, 'Code is required'),
    type: z.enum(['state', 'territory'], { required_error: 'Type is required' }),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isAdding.value) return
  formError.value = ''

  const formValues = values as StatePayload
  formValues.treatments = treatmentOptions.value
    .filter((item) => item.status)
    .map((item) => ({
      name: item.name as 'ED' | 'HL' | 'WL',
      visit_type: item.visit_type as 'asynchronous' | 'synchronous',
    }))

  const result = await addState(formValues)

  if (result) {
    router.push({ name: 'states' })
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof Omit<StatePayload, 'treatments'>, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to add state. Please try again.'
  }
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Add New State</h1>
    </div>

    <Card>
      <CardContent>
        <form class="space-y-6" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- State Information Section -->
          <div class="space-y-6">
            <h2 class="text-lg font-medium">State Information</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 items-start">
              <!-- State Name -->
              <FormField name="name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>State Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- State Code -->
              <FormField name="code" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>State Code</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter code"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Type Selector -->
              <FormField name="type" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Type</FormLabel>
                  <FormControl>
                    <Select :model-value="field.value" @update:model-value="field.onChange($event)">
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem v-for="opt in typeOptions" :key="opt.value" :value="opt.value">
                          {{ opt.label }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage v-if="errorMessage">{{ errorMessage }}</FormMessage>
                </FormItem>
              </FormField>

              <!-- Treatments Array -->
              <div class="col-span-2">
                <div class="mb-4">
                  <div class="text-base">Treatments</div>
                </div>

                <div v-for="item in treatmentOptions" :key="item.name" class="space-y-3 mb-3">
                  <div class="flex flex-row items-center space-x-2 mb-2">
                    <Checkbox :id="`name_${item.name}`" v-model="item.status" />
                    <label :for="`name_${item.name}`" class="font-normal">
                      {{ item.name }}
                    </label>
                  </div>
                  <div v-if="item.status" class="flex gap-3 ms-5">
                    <div
                      v-for="opt in visitTypeOptions"
                      :key="opt.value"
                      class="flex items-center gap-2"
                    >
                      <input
                        :id="`visit_type_${item.name}_${opt.value}`"
                        v-model="item.visit_type"
                        type="radio"
                        :name="`visit_type_${item.name}`"
                        class="size-4"
                        :value="opt.value"
                      />
                      <label :for="`visit_type_${item.name}_${opt.value}`" class="font-normal">
                        {{ opt.label }} ({{
                          opt.value === 'asynchronous' ? 'Text based' : 'Video based'
                        }})
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3 pt-4">
            <Button variant="outline" type="button" :disabled="isAdding" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isAdding">
              <Loader2 v-if="isAdding" class="mr-2 h-4 w-4 animate-spin" />
              {{ isAdding ? 'Adding...' : 'Add State' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
