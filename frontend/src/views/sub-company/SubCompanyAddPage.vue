<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useStateList } from '@/composables/useStateList'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectChipsValue,
} from '@/components/ui/select'
import { ArrowLeft } from 'lucide-vue-next'
import { vMaska } from 'maska/vue'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import {
  usePharmacyStore,
  useSubCompanyStore,
  useTreatmentStore,
  type PharmacyOption,
  type SubCompanyPayload,
} from '@/stores'
import { storeToRefs } from 'pinia'
import SubCompanyCredentialsDialog from '@/components/SubCompanyCredentialsDialog.vue'
import type { AcceptableValue } from 'reka-ui'

const subCompanyStore = useSubCompanyStore()
const {
  error: storeError,
  isAdding,
  fieldErrors,
  visitCreationApiKey,
  visitWebhookApiKey,
} = storeToRefs(subCompanyStore)
const { addSubCompany } = subCompanyStore
const treatmentStore = useTreatmentStore()
const { treatmentOptions, isLoadingTreatmentOptions } = storeToRefs(treatmentStore)
const { fetchTreatmentOptions } = treatmentStore
const pharmacyStore = usePharmacyStore()
const { pharmacyOptions, isLoadingPharmacyOptions } = storeToRefs(pharmacyStore)
const { fetchPharmacyOptions } = pharmacyStore
const { states, isLoadingStates, error: statesError } = useStateList()

const router = useRouter()
const formError = ref('')
const isCopyDialogOpen = ref(false)
const selectedPharmacies = ref<PharmacyOption[]>([])
const visitWebhookUrl = ref('')
const pharmacyWebhookUrl = ref('')

const baseSchema = {
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Please enter a valid email address'),
  phone_number: z.string().min(1, 'Phone number is required'),
  company_name: z.string().min(1, 'Company name is required'),
  company_id: z.string().min(1, 'Company ID is required'),
  company_website_url: z.string().url('Please enter a valid URL'),
  app_base_url: z.string().min(1, 'App base URL is required'),
  beluga_sub_company: z.string().min(1, 'Beluga sub company is required'),
  visit_prefix: z.string().min(1, 'Visit prefix is required').max(6),
  pharmacies: z.array(z.string()).min(1, 'Pharmacy is required'),
  treatment_ids: z.array(z.string()).min(1, 'Treatment is required'),
  address_line_1: z.string().min(1, 'Address line 1 is required'),
  address_line_2: z.string().optional().nullable(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(2, 'Please select a state'),
  country: z.string().min(1, 'Country is required'),
  zipcode: z.string().min(1, 'Zipcode is required'),
}

const getDynamicSchema = () => {
  const dynamicFields: Record<string, any> = {}

  selectedPharmacies.value.forEach((pharmacy, index) => {
    if (pharmacy.authorization_type === 'username_password') {
      dynamicFields[`pharmacies_${index}_username`] = z.string().optional()
      dynamicFields[`pharmacies_${index}_password`] = z.string().optional()
    }
  })

  return {
    ...baseSchema,
    ...dynamicFields,
  }
}

const formSchema = toTypedSchema(z.lazy(() => z.object(getDynamicSchema())))

type SubCompanyFormValues = Omit<SubCompanyPayload, 'pharmacies'> & {
  [key: string]: unknown
  pharmacies: string[]
}

const form = useForm<SubCompanyFormValues>({
  validationSchema: formSchema,
})

watch(
  () => form.values.pharmacies,
  (values) => {
    selectedPharmacies.value = []
    values?.forEach((value) => {
      const pharmacy = pharmacyOptions.value.find((ph) => ph.id === value)
      if (pharmacy) {
        selectedPharmacies.value.push(pharmacy)
      }
    })
  },
)

watch(
  () => form.values.app_base_url,
  (value) => {
    if (value) {
      const baseUrl = value.endsWith('/') ? value.slice(0, -1) : value
      visitWebhookUrl.value = `${baseUrl}/callback/provider-webhook`
      pharmacyWebhookUrl.value = `${baseUrl}/callback/pharmacy-webhook`
    }
  },
)

const onSubmit = form.handleSubmit(async (values) => {
  if (isAdding.value) return
  formError.value = ''

  const pharmaciesPayload = selectedPharmacies.value.map((pharmacy, index) => {
    if (pharmacy.authorization_type === 'username_password') {
      const username = values[`pharmacies_${index}_username`] as string | undefined
      const password = values[`pharmacies_${index}_password`] as string | undefined

      delete values[`pharmacies_${index}_username`]
      delete values[`pharmacies_${index}_password`]

      return {
        id: pharmacy.id,
        username,
        password,
      }
    }

    return null
  })

  const result = await addSubCompany({
    ...values,
    phone_number: values.phone_number.replace(/[^0-9]/g, ''),
    pharmacies: pharmaciesPayload.filter((pharmacy) => pharmacy !== null),
  })

  if (result) {
    isCopyDialogOpen.value = true
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof SubCompanyPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to add sub company. Please try again.'
  }
})

onMounted(async () => {
  await Promise.all([fetchTreatmentOptions(), fetchPharmacyOptions()])
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Add Sub-Company</h1>
    </div>

    <Card>
      <CardContent class="pt-6">
        <form class="space-y-10" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Contact Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Contact Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- First Name -->
              <FormField name="first_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter first name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Last Name -->
              <FormField name="last_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter last name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Email -->
              <FormField name="email" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="email"
                      placeholder="Enter email address"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Phone Number -->
              <FormField name="phone_number" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      v-maska="'(###) ###-####'"
                      placeholder="Enter phone number"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Company Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Company Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Company Name -->
              <FormField name="company_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter company name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Company ID -->
              <FormField name="company_id" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company ID</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter company ID"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Company Website URL -->
              <FormField name="company_website_url" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company Website URL</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="url"
                      placeholder="https://example.com"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- App Base URL -->
              <FormField name="app_base_url" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>App Base URL</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="url"
                      placeholder="https://example.com"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Beluga Sub Company -->
              <FormField name="beluga_sub_company" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Beluga Sub Company</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter beluga sub company"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Visit Prefix -->
              <FormField name="visit_prefix" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Visit Prefix</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter visit prefix"
                      :aria-invalid="!!errorMessage"
                      maxlength="6"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Treatment -->
              <FormField name="treatment_ids" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Treatments (Choose one or more)</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="isLoadingTreatmentOptions"
                      multiple
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectChipsValue placeholder="Select treatments">
                          <template #chip="{ value }">
                            {{
                              treatmentOptions.find((t) => t.id === value)?.treatment_name || value
                            }}
                          </template>
                        </SelectChipsValue>
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="isLoadingTreatmentOptions" class="p-2 text-center text-sm">
                          Loading treatments...
                        </div>
                        <div
                          v-else-if="treatmentOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No treatments available
                        </div>
                        <SelectItem
                          v-for="treatment in treatmentOptions"
                          :key="treatment.id"
                          :value="treatment.id"
                        >
                          {{ treatment.treatment_name }} ({{ treatment.beluga_visit_type }})
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacies -->
              <FormField name="pharmacies" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacies (Choose one or more)</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="isLoadingPharmacyOptions"
                      multiple
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectChipsValue placeholder="Select pharmacies">
                          <template #chip="{ value }">
                            {{
                              pharmacyOptions.find((p) => p.id === value)?.pharmacy_name || value
                            }}
                          </template>
                        </SelectChipsValue>
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="isLoadingPharmacyOptions" class="p-2 text-center text-sm">
                          Loading pharmacies...
                        </div>
                        <div
                          v-else-if="pharmacyOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No pharmacies available
                        </div>
                        <SelectItem
                          v-for="pharmacy in pharmacyOptions"
                          :key="pharmacy.id"
                          :value="pharmacy.id"
                        >
                          {{ pharmacy.pharmacy_name }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Pharmacy authentication fields -->
          <div v-for="(pharmacy, index) in selectedPharmacies" :key="pharmacy.id">
            <div v-if="pharmacy.authorization_type === 'username_password'">
              <h2 class="text-lg font-medium mb-4">{{ pharmacy.pharmacy_name }} Authentication</h2>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 items-start">
                <!-- Pharmacy Username -->
                <FormField
                  :name="`pharmacies_${index}_username`"
                  v-slot="{ componentField, errorMessage }"
                >
                  <FormItem>
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        :placeholder="`Enter ${pharmacy.pharmacy_name} username`"
                        :aria-invalid="!!errorMessage"
                        type="text"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Pharmacy Password -->
                <FormField
                  :name="`pharmacies_${index}_password`"
                  v-slot="{ componentField, errorMessage }"
                >
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        :placeholder="`Enter ${pharmacy.pharmacy_name} password`"
                        :aria-invalid="!!errorMessage"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            </div>
          </div>

          <!-- Webhook -->
          <div>
            <h2 class="text-lg font-medium mb-4">Internal Communication</h2>
            <div class="grid grid-cols-1 gap-4 items-start">
              <!-- Webhook URL -->
              <FormField name="visit_webhook_url" class="col-span-2">
                <FormItem>
                  <FormLabel>Visit Webhook URL</FormLabel>
                  <FormControl>
                    <Input
                      v-model="visitWebhookUrl"
                      placeholder="Visit Webhook URL"
                      disabled
                      readonly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacy Webhook URL -->
              <FormField name="pharmacy_webhook_url" class="col-span-2">
                <FormItem>
                  <FormLabel>Pharmacy Webhook URL</FormLabel>
                  <FormControl>
                    <Input
                      v-model="pharmacyWebhookUrl"
                      placeholder="Pharmacy Webhook URL"
                      disabled
                      readonly
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Address Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Address</h2>
            <div class="grid grid-cols-1 gap-4 items-start">
              <!-- Address Line 1 -->
              <FormField name="address_line_1" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Address Line 1</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter address line 1"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Address Line 2 -->
              <FormField name="address_line_2" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Address Line 2 (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter address line 2"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                <!-- City -->
                <FormField name="city" v-slot="{ componentField, errorMessage }">
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        placeholder="Enter city"
                        :aria-invalid="!!errorMessage"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- State -->
                <FormField name="state" v-slot="{ field, errorMessage }">
                  <FormItem>
                    <FormLabel>State</FormLabel>
                    <FormControl>
                      <Select
                        :model-value="field.value"
                        @update:model-value="field.onChange($event)"
                        :disabled="isLoadingStates"
                      >
                        <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                          <SelectValue placeholder="Select a state" />
                        </SelectTrigger>
                        <SelectContent>
                          <div v-if="isLoadingStates" class="p-2 text-center text-sm">
                            Loading states...
                          </div>
                          <div
                            v-else-if="statesError"
                            class="p-2 text-center text-sm text-destructive-foreground"
                          >
                            {{ statesError }}
                          </div>
                          <div v-else-if="states.length === 0" class="p-2 text-center text-sm">
                            No states available
                          </div>
                          <SelectItem v-for="state in states" :key="state.code" :value="state.code">
                            {{ state.name }}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Country -->
                <FormField name="country" v-slot="{ field, errorMessage }">
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Select
                        :model-value="field.value"
                        @update:model-value="field.onChange($event)"
                      >
                        <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                          <SelectValue placeholder="Select a country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USA">United States</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Zipcode -->
                <FormField name="zipcode" v-slot="{ componentField, errorMessage }">
                  <FormItem>
                    <FormLabel>Zipcode</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        v-maska="'#####'"
                        placeholder="Enter zipcode"
                        :aria-invalid="!!errorMessage"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3">
            <Button variant="outline" type="button" :disabled="isAdding" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isAdding">
              {{ isAdding ? 'Adding...' : 'Submit' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <SubCompanyCredentialsDialog
      v-model:open="isCopyDialogOpen"
      :visit-creation-api-key="visitCreationApiKey"
      :visit-webhook-api-key="visitWebhookApiKey"
      @update:open="
        (value) => {
          if (!value) {
            router.push({ name: 'sub-companies' })
          }
        }
      "
    />
  </div>
</template>
