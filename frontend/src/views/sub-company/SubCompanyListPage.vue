<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import dayjs from 'dayjs'
import { CalendarIcon, Search, PlusIcon, XIcon, Ellipsis, EyeIcon } from 'lucide-vue-next'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useSubCompanyStore } from '@/stores'
import type { AcceptableValue } from 'reka-ui'
import { cn, formatPhone } from '@/lib'
import { RangeCalendar } from '@/components/ui/range-calendar'
import { useDebounce } from '@vueuse/core'
import ConfirmationDialog from '@/components/ConfirmationDialog.vue'
import { toast } from 'vue-sonner'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import SubCompanyCredentialsDialog from '@/components/SubCompanyCredentialsDialog.vue'

const subCompanyStore = useSubCompanyStore()
const {
  subCompanies,
  isLoadingList,
  pagination,
  isDeleting,
  isTogglingStatus,
  searchQuery,
  statusFilter,
  dateRange,
  sortBy,
  isRegeneratingApiKey,
  visitCreationApiKey,
  visitWebhookApiKey,
  error: storeError,
} = storeToRefs(subCompanyStore)
const { fetchSubCompanies, toggleSubCompanyStatus, deleteSubCompany, regenerateApiKey } =
  subCompanyStore

const router = useRouter()
const debouncedSearchQuery = useDebounce(searchQuery, 300)
const isRegeneratingApiKeyDialogOpen = ref(false)
const isRegeneratingWebhookKeyDialogOpen = ref(false)
const selectedCompanyId = ref<string>('')
const isDeleteDialogOpen = ref(false)
const isCopyDialogOpen = ref(false)

async function handleSearch() {
  pagination.value.currentPage = 1
  await fetchSubCompanies()
}

async function handleSort(column: string) {
  if (sortBy.value.column === column) {
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value.column = column
    sortBy.value.direction = 'desc'
  }
  await fetchSubCompanies()
}

async function handlePageChange(page: number) {
  pagination.value.currentPage = page
  await fetchSubCompanies()
}

async function handlePerPageChange(value: AcceptableValue) {
  pagination.value.perPage = Number(value)
  pagination.value.currentPage = 1
  await fetchSubCompanies()
}

async function handleStatusToggle(id: string) {
  if (isTogglingStatus.value) return

  const result = await toggleSubCompanyStatus(id)

  if (result) {
    await fetchSubCompanies()
  } else {
    toast.error(storeError.value || 'Failed to toggle status. Please try again later.')
  }
}

async function resetFilters() {
  searchQuery.value = ''
  statusFilter.value = 'all'
  dateRange.value = { start: undefined, end: undefined }
  sortBy.value = { column: '', direction: 'desc' }
  pagination.value.currentPage = 1
  pagination.value.perPage = 10
  await fetchSubCompanies()
}

async function deleteAccount(companyId: string) {
  if (isDeleting.value) return

  const result = await deleteSubCompany(companyId)

  if (result) {
    await fetchSubCompanies()
  } else {
    toast.error(storeError.value || 'Failed to delete. Please try again later.')
  }
}

async function handleRegenerateKey(id: string, type: 'visit' | 'webhook') {
  if (isRegeneratingApiKey.value) return

  const result = await regenerateApiKey(id, type)

  if (result) {
    isCopyDialogOpen.value = true
  } else {
    toast.error(storeError.value || 'Failed to regenerate. Please try again later.')
  }
}

watch(
  () => debouncedSearchQuery.value,
  () => {
    handleSearch()
  },
)

onMounted(async () => {
  await fetchSubCompanies()
  if (storeError.value) {
    toast.error(storeError.value)
  }
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">Sub Companies</h1>
        <p class="text-muted-foreground text-sm">Manage your sub-companies and their settings</p>
      </div>
      <Button @click="router.push({ name: 'add-sub-company' })">
        <PlusIcon class="h-4 w-4" />
        Add Sub-Company
      </Button>
    </div>

    <Card>
      <CardHeader class="pb-3">
        <div
          class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-x-4 sm:space-y-0"
        >
          <div class="relative w-full sm:max-w-sm">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="searchQuery"
              placeholder="Search sub-companies..."
              class="pl-8"
              @keyup.enter="handleSearch"
            />
            <XIcon
              v-if="searchQuery"
              class="absolute right-2.5 top-2.5 h-4 w-4 cursor-pointer text-muted-foreground"
              @click="searchQuery = ''"
            />
          </div>
          <div class="flex items-center space-x-2">
            <Select v-model="statusFilter" @update:model-value="handleSearch">
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="1">Active</SelectItem>
                <SelectItem value="0">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <Popover>
              <PopoverTrigger as-child>
                <Button
                  id="date"
                  variant="outline"
                  :class="
                    cn(
                      'min-w-[220px] justify-start text-left font-normal',
                      !dateRange && 'text-muted-foreground',
                    )
                  "
                >
                  <CalendarIcon class="mr-2 h-4 w-4" />
                  <template v-if="dateRange.start">
                    <template v-if="dateRange.end">
                      {{ dayjs(dateRange.start.toString()).format('MMM DD, YYYY') }} -
                      {{ dayjs(dateRange.end.toString()).format('MMM DD, YYYY') }}
                    </template>
                    <template v-else>
                      {{ dayjs(dateRange.start.toString()).format('MMM DD, YYYY') }}
                    </template>
                  </template>
                  <template v-else> Filter by date range </template>
                </Button>
              </PopoverTrigger>
              <PopoverContent class="w-auto p-0" align="end">
                <RangeCalendar
                  v-model="dateRange"
                  initial-focus
                  :number-of-months="1"
                  @update:start-value="(startDate) => (dateRange.start = startDate)"
                  @update:model-value="handleSearch"
                />
              </PopoverContent>
            </Popover>

            <Button variant="outline" @click="resetFilters"> Reset </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead>Company ID</TableHead>
                <TableHead>Company Name</TableHead>
                <TableHead>Contact Person</TableHead>
                <TableHead>Status</TableHead>
                <TableHead class="cursor-pointer" @click="handleSort('created_at')">
                  <div class="flex items-center">
                    Created Date
                    <span v-if="sortBy.column === 'created_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead class="cursor-pointer" @click="handleSort('updated_at')">
                  <div class="flex items-center">
                    Updated Date
                    <span v-if="sortBy.column === 'updated_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead class="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <template v-if="isLoadingList">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center"> Loading... </TableCell>
                </TableRow>
              </template>
              <template v-else-if="subCompanies.length === 0">
                <TableRow>
                  <TableCell colspan="9" class="h-24 text-center"> No results found. </TableCell>
                </TableRow>
              </template>
              <template v-else>
                <TableRow v-for="(company, index) in subCompanies" :key="company.id">
                  <TableCell class="font-medium">{{
                    index + 1 + (pagination.currentPage - 1) * pagination.perPage
                  }}</TableCell>
                  <TableCell class="font-medium">{{ company.company_id }}</TableCell>
                  <TableCell>{{ company.company_name }}</TableCell>
                  <TableCell>
                    <div>
                      <span>{{ company.first_name }} {{ company.last_name }}</span>
                      <span class="text-xs text-muted-foreground block">
                        {{ company.email }}
                      </span>
                      <span class="text-xs text-muted-foreground block">
                        {{ formatPhone(company.phone_number) }}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-2">
                      <Switch
                        v-model="company.status"
                        class="cursor-pointer"
                        @click.stop="handleStatusToggle(company.id)"
                      />
                      <span :class="company.is_active ? 'text-green-600' : 'text-muted-foreground'">
                        {{ company.is_active ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ company.created_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ company.created_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ company.updated_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ company.updated_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      @click="
                        router.push({
                          name: 'view-sub-company',
                          params: { company_id: company.id },
                        })
                      "
                    >
                      <span class="sr-only">View Details</span>
                      <EyeIcon class="h-4 w-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger>
                        <Button variant="ghost" size="icon">
                          <Ellipsis class="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          @click="
                            router.push({
                              name: 'edit-sub-company',
                              params: { company_id: company.id },
                            })
                          "
                          >Edit</DropdownMenuItem
                        >
                        <DropdownMenuItem
                          @click="
                            () => {
                              selectedCompanyId = company.id
                              isRegeneratingApiKeyDialogOpen = true
                            }
                          "
                          >Regenerate Visit API Key</DropdownMenuItem
                        >
                        <DropdownMenuItem
                          @click="
                            () => {
                              selectedCompanyId = company.id
                              isRegeneratingWebhookKeyDialogOpen = true
                            }
                          "
                          >Regenerate Visit Webhook Key</DropdownMenuItem
                        >
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          class="text-red-500"
                          @click="
                            () => {
                              selectedCompanyId = company.id
                              isDeleteDialogOpen = true
                            }
                          "
                          >Delete</DropdownMenuItem
                        >
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              </template>
            </TableBody>
          </Table>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between px-2 mt-4">
          <div class="flex items-center space-x-2">
            <p class="text-sm text-muted-foreground">
              Page {{ pagination.currentPage }} of {{ pagination.totalPages }}
            </p>
            <Select :model-value="pagination.perPage" @update:model-value="handlePerPageChange">
              <SelectTrigger class="h-8">
                <SelectValue :placeholder="pagination.perPage.toString()" />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem
                  v-for="pageSize in [10, 25, 50, 100]"
                  :key="pageSize"
                  :value="pageSize"
                  class="cursor-pointer"
                >
                  {{ pageSize }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage === 1"
              @click="handlePageChange(pagination.currentPage - 1)"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage >= pagination.totalPages"
              @click="handlePageChange(pagination.currentPage + 1)"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Delete Confirmation Dialog -->
    <ConfirmationDialog
      v-model:open="isDeleteDialogOpen"
      title="Are you absolutely sure?"
      description="This action will delete the sub-company and all its data. This action cannot be undone."
      confirmText="Delete"
      @confirm="deleteAccount(selectedCompanyId)"
    />

    <!-- Regenerate Visit API Key Dialog -->
    <ConfirmationDialog
      v-model:open="isRegeneratingApiKeyDialogOpen"
      title="Regenerate Visit API Key"
      description="Are you sure you want to regenerate the visit API key? This will invalidate the current key."
      confirmText="Regenerate"
      @confirm="() => handleRegenerateKey(selectedCompanyId, 'visit')"
    />

    <!-- Regenerate Visit Webhook Key Dialog -->
    <ConfirmationDialog
      v-model:open="isRegeneratingWebhookKeyDialogOpen"
      title="Regenerate Visit Webhook Key"
      description="Are you sure you want to regenerate the visit webhook key? This will invalidate the current key."
      confirmText="Regenerate"
      @confirm="() => handleRegenerateKey(selectedCompanyId, 'webhook')"
    />

    <!-- Copy Visit API Keys Dialog -->
    <SubCompanyCredentialsDialog
      v-model:open="isCopyDialogOpen"
      :visit-creation-api-key="visitCreationApiKey"
      :visit-webhook-api-key="visitWebhookApiKey"
      @update:open="
        (value) => {
          if (!value) {
            router.push({ name: 'sub-companies' })
          }
        }
      "
    />
  </div>
</template>
