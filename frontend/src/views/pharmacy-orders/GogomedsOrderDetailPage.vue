<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { useGogomedsOrdersStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { onMounted, ref } from 'vue'
import {
  Loader2Icon,
  ArrowLeft,
  EyeIcon,
  ExternalLinkIcon,
  PillIcon,
  TruckIcon,
  ClockIcon,
  MapPinIcon,
} from 'lucide-vue-next'
import router from '@/router'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Badge from '@/components/ui/badge/Badge.vue'
import { toast } from 'vue-sonner'

const gogomedsOrdersStore = useGogomedsOrdersStore()
const {
  gogomedsOrderDetail,
  isLoadingOrderDetail,
  orderEventLogDetail,
  isLoadingOrderEventLogDetail,
  error: storeError,
} = storeToRefs(gogomedsOrdersStore)
const {
  fetchGogomedsOrderDetail,
  fetchGogomedsOrderEventLogDetail,
  resolveGogomedsOrderStatusColor,
  retrieveOrderDetailsFromGogomeds,
} = gogomedsOrdersStore

const route = useRoute()
const orderId = route.params.order_id
const isOrderEventLogDetailOpen = ref(false)

async function openVisitLogDetail(logId: string, type: 'gogomeds' | 'sub_company') {
  isOrderEventLogDetailOpen.value = true
  await fetchGogomedsOrderEventLogDetail(logId, type)
  if (storeError.value) {
    toast.error(storeError.value)
    isOrderEventLogDetailOpen.value = false
  }
}

function openTrackingUrl(url: string) {
  window.open(url, '_blank')
}

onMounted(async () => {
  if (orderId) {
    await fetchGogomedsOrderDetail(orderId as string)
    if (storeError.value) {
      toast.error(storeError.value)
      router.back()
    }
  }
})
</script>

<template>
  <div class="min-h-screen">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">GoGoMeds Order Detail</h1>
      <Button
        class="ml-auto mb-4"
        :disabled="isLoadingOrderDetail"
        @click="retrieveOrderDetailsFromGogomeds(orderId as string)"
      >
        Fetch Details
      </Button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingOrderDetail" class="flex flex-col justify-center items-center gap-3 h-64">
      <Loader2Icon class="animate-spin h-8 w-8" />
      <span class="text-gray-500">Loading...</span>
    </div>

    <!-- Actual Content -->
    <div v-else-if="gogomedsOrderDetail" class="space-y-6">
      <!-- Responsive Card Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Visit Information -->
        <Card>
          <CardHeader>
            <CardTitle>Order Information</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell><Label>Sub Company Order No</Label></TableCell>
                  <TableCell>{{
                    gogomedsOrderDetail?.order_details?.sub_company_order_no ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Pharmacy Order No</Label></TableCell>
                  <TableCell>{{
                    gogomedsOrderDetail?.order_details?.pharmacy_order_no ?? '—'
                  }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Order Type</Label></TableCell>
                  <TableCell>{{ gogomedsOrderDetail?.order_details?.order_type }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Order Status</Label></TableCell>
                  <TableCell>
                    <Badge
                      class="text-foreground"
                      :class="
                        resolveGogomedsOrderStatusColor(gogomedsOrderDetail?.order_details?.status)
                      "
                    >
                      {{ gogomedsOrderDetail?.order_details?.order_status_text ?? '—' }}
                    </Badge>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Created At</Label></TableCell>
                  <TableCell>{{ gogomedsOrderDetail?.order_details?.created_at }}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell><Label>Updated At</Label></TableCell>
                  <TableCell>{{ gogomedsOrderDetail?.order_details?.updated_at }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <!-- Shipment Details -->
        <Card v-if="gogomedsOrderDetail.shipment_details">
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <TruckIcon class="h-5 w-5" />
              Shipment Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Shipment Method</Label>
                <p class="font-medium">
                  {{ gogomedsOrderDetail.shipment_details.shipment_method || '—' }}
                </p>
              </div>
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Delivery Provider</Label>
                <p>{{ gogomedsOrderDetail.shipment_details.delivery_provider || '—' }}</p>
              </div>
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Tracking Number</Label>
                <p class="font-mono text-sm">
                  {{ gogomedsOrderDetail.shipment_details.tracking_number || '—' }}
                </p>
              </div>
              <div>
                <Label class="text-sm font-medium text-muted-foreground">Tracking URL</Label>
                <div v-if="gogomedsOrderDetail.shipment_details.tracking_url">
                  <Button
                    variant="link"
                    size="sm"
                    class="p-0 h-auto"
                    @click="openTrackingUrl(gogomedsOrderDetail.shipment_details.tracking_url!)"
                  >
                    <ExternalLinkIcon class="h-4 w-4 mr-1" />
                    Track Package
                  </Button>
                </div>
                <p v-else class="text-muted-foreground">—</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Drugs/Medications -->
      <Card v-if="gogomedsOrderDetail.drugs && gogomedsOrderDetail.drugs.length > 0">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <PillIcon class="h-5 w-5" />
            Medications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-6">
            <div
              v-for="(drug, index) in gogomedsOrderDetail.drugs"
              :key="index"
              class="border rounded-lg p-4 space-y-4"
            >
              <!-- Drug Basic Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">NDC</Label>
                  <p class="font-mono text-sm">{{ drug.ndc || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Medicine Name</Label>
                  <p class="font-medium">{{ drug.medicine_name || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground"
                    >Full Medicine Name</Label
                  >
                  <p class="text-sm">{{ drug.full_medicine_name || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Dose</Label>
                  <p>{{ drug.dose || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Form</Label>
                  <p>{{ drug.form || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Packaging</Label>
                  <p>{{ drug.packaging || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground">Quantity</Label>
                  <p class="font-medium">{{ drug.qty || '—' }}</p>
                </div>
                <div>
                  <Label class="text-sm font-medium text-muted-foreground"
                    >Refill Script Number</Label
                  >
                  <p class="font-mono text-sm">{{ drug.refill_script_number || '—' }}</p>
                </div>
              </div>

              <!-- Prescriber Information -->
              <div v-if="drug.prescriber" class="border-t pt-4">
                <h4 class="font-medium mb-3">Prescriber Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground">Name</Label>
                    <p>
                      {{
                        `${drug.prescriber.first_name || ''} ${drug.prescriber.last_name || ''}`.trim() ||
                        '—'
                      }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground">NPI</Label>
                    <p class="font-mono text-sm">{{ drug.prescriber.npi || '—' }}</p>
                  </div>
                  <div v-if="drug.prescriber.address">
                    <Label class="text-sm font-medium text-muted-foreground">Address</Label>
                    <p class="text-sm">
                      {{
                        `${drug.prescriber.address.Line1 || ''} ${
                          drug.prescriber.address.Line2 || ''
                        } ${drug.prescriber.address.City || ''} ${drug.prescriber.address.State || ''} ${
                          drug.prescriber.address.Zip || ''
                        }`.trim() || '—'
                      }}
                    </p>
                  </div>
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground">Fax Number</Label>
                    <p class="font-mono text-sm">{{ drug.prescriber.fax_number || '—' }}</p>
                  </div>
                </div>
              </div>

              <!-- Order Item Status -->
              <div
                v-if="drug.order_item_status && drug.order_item_status.length > 0"
                class="border-t pt-4"
              >
                <h4 class="font-medium mb-3">Order Item Status History</h4>
                <div class="space-y-2">
                  <div
                    v-for="(status, statusIndex) in drug.order_item_status"
                    :key="statusIndex"
                    class="flex items-center justify-between p-2 bg-secondary/50 rounded"
                  >
                    <div class="flex items-center gap-2">
                      <Badge variant="secondary">{{ status.status_name || '—' }}</Badge>
                      <span class="text-sm text-muted-foreground"
                        >by {{ status.status_set_by || '—' }}</span
                      >
                    </div>
                    <span class="text-sm text-muted-foreground">{{ status.status_set_date }}</span>
                  </div>
                </div>
              </div>

              <!-- Prescription Information -->
              <div v-if="drug.prescription" class="border-t pt-4">
                <h4 class="font-medium mb-3">Prescription Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground">Written Date</Label>
                    <p>{{ drug.prescription.written_date }}</p>
                  </div>
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground"
                      >Written Quantity</Label
                    >
                    <p class="font-medium">{{ drug.prescription.written_qty || '—' }}</p>
                  </div>
                  <div>
                    <Label class="text-sm font-medium text-muted-foreground">Script Number</Label>
                    <p class="font-mono text-sm">{{ drug.prescription.script_no || '—' }}</p>
                  </div>
                </div>

                <!-- Dispense History -->
                <div
                  v-if="
                    drug.prescription.dispense_history &&
                    drug.prescription.dispense_history.length > 0
                  "
                >
                  <h5 class="font-medium mb-2 text-sm">Dispense History</h5>
                  <div class="space-y-1">
                    <div
                      v-for="(dispense, dispenseIndex) in drug.prescription.dispense_history"
                      :key="dispenseIndex"
                      class="flex items-center justify-between text-sm p-2 bg-muted/50 rounded"
                    >
                      <span class="font-medium">Qty: {{ dispense.dispense_qty || '—' }}</span>
                      <span class="text-muted-foreground">{{ dispense.dispense_date }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- No Drugs Message -->
      <Card v-else-if="gogomedsOrderDetail.drugs && gogomedsOrderDetail.drugs.length === 0">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <PillIcon class="h-5 w-5" />
            Medications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="text-center py-6">
            <p class="text-muted-foreground">No medications found for this order.</p>
          </div>
        </CardContent>
      </Card>

      <!-- Order Status History -->
      <Card
        v-if="
          gogomedsOrderDetail.order_status_history &&
          gogomedsOrderDetail.order_status_history.length > 0
        "
      >
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <ClockIcon class="h-5 w-5" />
            Order Status History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="max-h-64 overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Status</TableHead>
                  <TableHead>Set By</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow
                  v-for="(history, index) in gogomedsOrderDetail.order_status_history"
                  :key="index"
                >
                  <TableCell>
                    <Badge variant="secondary">{{ history.status || '—' }}</Badge>
                  </TableCell>
                  <TableCell>{{ history.status_set_by || '—' }}</TableCell>
                  <TableCell>{{ history.status_set_date }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <!-- Order Ship Details -->
      <Card
        v-if="
          gogomedsOrderDetail.order_ship_details &&
          gogomedsOrderDetail.order_ship_details.length > 0
        "
      >
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <MapPinIcon class="h-5 w-5" />
            Shipping Tracking Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="max-h-96 overflow-y-auto">
            <div class="relative">
              <!-- Timeline line -->
              <div class="absolute left-[7px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

              <div class="space-y-6">
                <div
                  v-for="(shipDetail, index) in gogomedsOrderDetail.order_ship_details"
                  :key="index"
                  class="relative flex items-start space-x-6"
                >
                  <!-- Timeline dot -->
                  <div class="relative z-10 flex-shrink-0">
                    <div
                      class="w-4 h-4 rounded-full border-4 bg-white"
                      :class="
                        shipDetail.EventStatus === 'Delivered'
                          ? 'border-green-500'
                          : 'border-gray-400'
                      "
                    >
                      <div
                        class="w-2 h-2 rounded-full absolute top-1 left-1"
                        :class="
                          shipDetail.EventStatus === 'Delivered' ? 'bg-green-500' : 'bg-gray-400'
                        "
                      ></div>
                    </div>
                  </div>

                  <!-- Content -->
                  <div class="flex-1 min-w-0 pb-6">
                    <div class="flex items-start justify-between mb-2">
                      <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                          <Badge
                            :variant="
                              shipDetail.EventStatus === 'Delivered' ? 'default' : 'outline'
                            "
                            class="font-medium"
                          >
                            {{ shipDetail.EventStatus || '—' }}
                          </Badge>
                          <span class="text-sm font-medium text-muted-foreground">
                            {{ shipDetail.EventDate || '—' }}
                          </span>
                        </div>
                        <h4 class="text-base font-semibold text-foreground mb-1">
                          {{ shipDetail.EventDescription || '—' }}
                        </h4>
                        <div class="flex items-center gap-2 text-sm text-muted-foreground">
                          <MapPinIcon class="h-4 w-4" />
                          <span v-if="shipDetail.EventLocation">{{
                            shipDetail.EventLocation || 'Location not specified'
                          }}</span>
                        </div>
                        <p
                          v-if="
                            shipDetail.EventStatusText &&
                            shipDetail.EventStatusText !== shipDetail.EventDescription
                          "
                          class="text-sm text-muted-foreground mt-2 italic"
                        >
                          {{ shipDetail.EventStatusText }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Pharmacy Logs -->
      <Card
        v-if="gogomedsOrderDetail.pharmacy_logs && gogomedsOrderDetail.pharmacy_logs.length > 0"
      >
        <CardHeader>
          <CardTitle>Pharmacy Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead>Status Code</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Updated At</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="log in gogomedsOrderDetail.pharmacy_logs" :key="log.id">
                <TableCell>{{ log.request_from ?? '—' }}</TableCell>
                <TableCell>{{ log.request_to ?? '—' }}</TableCell>
                <TableCell>
                  <Badge
                    v-if="log.event_status_code && log.event_status_code === '200'"
                    variant="default"
                  >
                    {{ log.event_status_code }}
                  </Badge>
                  <Badge
                    v-else-if="log.event_status_code && log.event_status_code !== '200'"
                    variant="destructive"
                  >
                    {{ log.event_status_code }}
                  </Badge>
                  <span v-else> — </span>
                </TableCell>
                <TableCell>
                  <div>{{ log.created_date }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ log.created_time }}
                  </div>
                </TableCell>
                <TableCell>
                  <div>{{ log.updated_date }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ log.updated_time }}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="link"
                    size="icon"
                    @click="openVisitLogDetail(log.id, 'gogomeds')"
                  >
                    <EyeIcon class="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <!-- Sub Company Visit Logs -->
      <Card
        v-if="
          gogomedsOrderDetail.sub_company_pharmacy_logs &&
          gogomedsOrderDetail.sub_company_pharmacy_logs.length > 0
        "
      >
        <CardHeader>
          <CardTitle>Sub Company Visit Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Event</TableHead>
                <TableHead>From</TableHead>
                <TableHead>To</TableHead>
                <TableHead>Status Code</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Updated At</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="log in gogomedsOrderDetail.sub_company_pharmacy_logs" :key="log.id">
                <TableCell>
                  <Badge
                    v-if="log.event && log.response_code"
                    :variant="log.response_code === 200 ? 'secondary' : 'destructive'"
                  >
                    {{ log.event }}
                  </Badge>
                  <Badge v-else-if="log.event && !log.response_code" variant="secondary">
                    {{ log.event }}
                  </Badge>
                  <Badge v-else> — </Badge>
                </TableCell>
                <TableCell>{{ log.request_from ?? '—' }}</TableCell>
                <TableCell>{{ log.request_to ?? '—' }}</TableCell>
                <TableCell>
                  <Badge v-if="log.response_code && log.response_code === 200" variant="default">
                    {{ log.response_code }}
                  </Badge>
                  <Badge
                    v-else-if="log.response_code && log.response_code !== 200"
                    variant="destructive"
                  >
                    {{ log.response_code }}
                  </Badge>
                  <span v-else> — </span>
                </TableCell>
                <TableCell>
                  <div>{{ log.created_date }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ log.created_time }}
                  </div>
                </TableCell>
                <TableCell>
                  <div>{{ log.updated_date }}</div>
                  <div class="text-xs text-muted-foreground">
                    {{ log.updated_time }}
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="link"
                    size="icon"
                    @click="openVisitLogDetail(log.id, 'sub_company')"
                  >
                    <EyeIcon class="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else class="flex flex-col items-center justify-center py-12">
      <div class="text-center">
        <h3 class="text-lg font-medium text-muted-foreground mb-2">No Order Details Found</h3>
        <p class="text-sm text-muted-foreground">
          The order details could not be loaded or do not exist.
        </p>
      </div>
    </div>

    <!-- Visit Log Detail -->
    <Dialog v-model:open="isOrderEventLogDetailOpen">
      <DialogContent class="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Visit Log Detail</DialogTitle>
        </DialogHeader>
        <div v-if="isLoadingOrderEventLogDetail" class="grid place-items-center">
          <Loader2Icon class="animate-spin h-8 w-8" />
          <span class="text-gray-500">Loading...</span>
        </div>
        <div v-else class="flex items-center space-x-2 w-full overflow-auto bg-secondary rounded">
          <pre v-if="orderEventLogDetail" class="p-4 max-h-96">{{ orderEventLogDetail }}</pre>
          <div v-else class="text-center py-3">
            <pre class="text-disabled p-4">No Data Available</pre>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>
