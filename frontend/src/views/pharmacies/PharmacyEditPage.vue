<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { usePharmacyStore, type PharmacyPayload } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { storeToRefs } from 'pinia'

const pharmacyStore = usePharmacyStore()
const {
  selectedPharmacy,
  isUpdating,
  isLoadingDetails,
  error: storeError,
  fieldErrors,
} = storeToRefs(pharmacyStore)
const { updatePharmacy, getPharmacyById } = pharmacyStore

const route = useRoute()
const router = useRouter()

const formError = ref('')
const pharmacyId = route.params.pharmacy_id as string
const pharmacyFormData = ref<PharmacyPayload>({
  pharmacy_name: '',
  pharmacy_type: '',
  pharmacy_id: '',
})

async function fetchPharmacyDetails() {
  if (isLoadingDetails.value) return

  if (!pharmacyId) return router.push({ name: 'pharmacies' })

  const result = await getPharmacyById(pharmacyId)

  if (result) {
    pharmacyFormData.value = {
      pharmacy_name: selectedPharmacy.value?.pharmacy_name || '',
      pharmacy_type: selectedPharmacy.value?.pharmacy_type || '',
      pharmacy_id: selectedPharmacy.value?.pharmacy_id || '',
    }
  } else {
    toast.error(storeError.value || 'Failed to fetch pharmacy details')
    router.push({ name: 'pharmacies' })
  }
}

const formSchema = toTypedSchema(
  z.object({
    pharmacy_name: z.string().min(1, 'Pharmacy name is required'),
    pharmacy_type: z.string().min(1, 'Pharmacy type is required'),
    pharmacy_id: z.string().min(1, 'Pharmacy ID is required'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
  initialValues: pharmacyFormData.value,
})

watch(
  () => pharmacyFormData.value,
  () => {
    form.setValues(pharmacyFormData.value)
  },
)

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isUpdating.value) return
  formError.value = ''

  const formValues = values as PharmacyPayload & { id: string }
  formValues.id = pharmacyId

  const result = await updatePharmacy(formValues)

  if (result) {
    router.push({ name: 'pharmacies' })
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof PharmacyPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to update pharmacy. Please try again.'
  }
})

onMounted(async () => {
  await fetchPharmacyDetails()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Edit Pharmacy</h1>
    </div>

    <Card>
      <CardContent>
        <!-- Loading -->
        <div v-if="isLoadingDetails" class="text-center py-8">
          <Loader2 class="animate-spin h-8 w-8 text-gray-500 mx-auto" />
        </div>

        <!-- Form -->
        <form v-else class="space-y-8" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Pharmacy Information Section -->
          <div class="space-y-6">
            <h2 class="text-lg font-medium">Pharmacy Information</h2>
            <div class="grid grid-cols-1 gap-6">
              <!-- Pharmacy Name -->
              <FormField name="pharmacy_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacy Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacy Type -->
              <FormField name="pharmacy_type" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacy Type</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy type"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacy ID -->
              <FormField name="pharmacy_id" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacy ID</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy ID"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3 pt-4">
            <Button variant="outline" type="button" :disabled="isUpdating" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isUpdating">
              <Loader2 v-if="isUpdating" class="mr-2 h-4 w-4 animate-spin" />
              {{ isUpdating ? 'Updating...' : 'Update Pharmacy' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
