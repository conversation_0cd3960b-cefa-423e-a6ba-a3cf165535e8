<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useTreatmentStore, type TreatmentPayload } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { storeToRefs } from 'pinia'

const treatmentStore = useTreatmentStore()
const { isAdding, error: storeError, fieldErrors } = storeToRefs(treatmentStore)
const { addTreatment } = treatmentStore

const router = useRouter()
const formError = ref('')

const formSchema = toTypedSchema(
  z.object({
    treatment_name: z.string().min(1, 'Treatment name is required'),
    beluga_visit_type: z.string().min(1, 'Beluga visit type is required'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isAdding.value) return
  formError.value = ''

  const formValues = values as TreatmentPayload
  const result = await addTreatment(formValues)

  if (result) {
    router.push({ name: 'treatments' })
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof TreatmentPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to add treatment. Please try again.'
  }
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Add New Treatment</h1>
    </div>

    <Card>
      <CardContent>
        <form class="space-y-6" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Treatment Information Section -->
          <div class="space-y-6">
            <h2 class="text-lg font-medium">Treatment Information</h2>
            <div class="grid grid-cols-1 gap-6">
              <!-- Treatment Name -->
              <FormField name="treatment_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Treatment Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter treatment name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Beluga Visit Type -->
              <FormField name="beluga_visit_type" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Beluga Visit Type</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter beluga visit type (e.g., ed, hairloss)"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3 pt-4">
            <Button variant="outline" type="button" :disabled="isAdding" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isAdding">
              <Loader2 v-if="isAdding" class="mr-2 h-4 w-4 animate-spin" />
              {{ isAdding ? 'Adding...' : 'Add Treatment' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
