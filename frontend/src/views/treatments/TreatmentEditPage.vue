<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useTreatmentStore, type TreatmentPayload } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { storeToRefs } from 'pinia'

const treatmentStore = useTreatmentStore()
const {
  selectedTreatment,
  isUpdating,
  isLoadingDetails,
  error: storeError,
  fieldErrors,
} = storeToRefs(treatmentStore)
const { updateTreatment, getTreatmentById } = treatmentStore

const route = useRoute()
const router = useRouter()

const formError = ref('')
const treatmentId = route.params.treatment_id as string
const treatmentFormData = ref<TreatmentPayload>({
  treatment_name: '',
  beluga_visit_type: '',
})

async function fetchTreatmentDetails() {
  if (isLoadingDetails.value) return

  if (!treatmentId) return router.push({ name: 'treatments' })

  const result = await getTreatmentById(treatmentId)

  if (result) {
    treatmentFormData.value = {
      treatment_name: selectedTreatment.value?.treatment_name || '',
      beluga_visit_type: selectedTreatment.value?.beluga_visit_type || '',
    }
  } else {
    toast.error(storeError.value || 'Failed to fetch treatment details')
    router.push({ name: 'treatments' })
  }
}

const formSchema = toTypedSchema(
  z.object({
    treatment_name: z.string().min(1, 'Treatment name is required'),
    beluga_visit_type: z.string().min(1, 'Beluga visit type is required'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
  initialValues: treatmentFormData.value,
})

watch(
  () => treatmentFormData.value,
  () => {
    form.setValues(treatmentFormData.value)
  },
)

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isUpdating.value) return
  formError.value = ''

  const formValues = values as TreatmentPayload & { id: string }
  formValues.id = treatmentId

  const result = await updateTreatment(formValues)

  if (result) {
    router.push({ name: 'treatments' })
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof TreatmentPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to update treatment. Please try again.'
  }
})

onMounted(async () => {
  await fetchTreatmentDetails()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Edit Treatment</h1>
    </div>

    <Card>
      <CardContent>
        <!-- Loading -->
        <div v-if="isLoadingDetails" class="text-center py-8">
          <Loader2 class="animate-spin h-8 w-8 text-gray-500 mx-auto" />
        </div>

        <!-- Form -->
        <form v-else class="space-y-8" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Treatment Information Section -->
          <div class="space-y-6">
            <h2 class="text-lg font-medium">Treatment Information</h2>
            <div class="grid grid-cols-1 gap-6">
              <!-- Treatment Name -->
              <FormField name="treatment_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Treatment Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter treatment name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Beluga Visit Type -->
              <FormField name="beluga_visit_type" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Beluga Visit Type</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter beluga visit type (e.g., ed, hairloss)"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3 pt-4">
            <Button variant="outline" type="button" :disabled="isUpdating" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isUpdating">
              <Loader2 v-if="isUpdating" class="mr-2 h-4 w-4 animate-spin" />
              {{ isUpdating ? 'Updating...' : 'Update Treatment' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
